#!/usr/bin/env python3
"""
Motion Agent Project Launcher
Motion Agent 项目启动器
"""

import subprocess
import sys
import os
from pathlib import Path


def main():
    """主启动函数"""
    print("🎬 Motion Agent Project Launcher")
    print("=" * 40)
    print("Motion Agent 项目启动器")
    print("=" * 40)
    
    print("\n请选择要启动的服务:")
    print("1. 启动后端服务 (Backend)")
    print("2. 启动前端服务 (Frontend)")
    print("3. 同时启动前后端 (Full Stack)")
    print("4. 运行测试 (Run Tests)")
    print("5. 退出 (Exit)")

    try:
        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == "1":
            start_backend()
        elif choice == "2":
            start_frontend()
        elif choice == "3":
            start_fullstack()
        elif choice == "4":
            run_tests()
        elif choice == "5":
            print("👋 再见!")
            sys.exit(0)
        else:
            print("❌ 无效选择，请重新运行")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 用户取消")
        sys.exit(0)


def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    backend_script = Path("backend/start_backend.py")
    
    if not backend_script.exists():
        print(f"❌ 后端启动脚本不存在: {backend_script}")
        sys.exit(1)
    
    try:
        subprocess.run([sys.executable, str(backend_script)])
    except Exception as e:
        print(f"💥 启动后端失败: {e}")


def start_frontend():
    """启动前端服务"""
    print("\n🌐 启动前端服务...")
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print(f"❌ 前端目录不存在: {frontend_dir}")
        sys.exit(1)
    
    # 检查是否有 package.json
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print(f"❌ package.json 不存在: {package_json}")
        sys.exit(1)
    
    try:
        # 使用 pnpm 启动前端
        subprocess.run(["pnpm", "dev"], cwd=frontend_dir)
    except FileNotFoundError:
        print("❌ pnpm 未安装，尝试使用 npm...")
        try:
            subprocess.run(["npm", "run", "dev"], cwd=frontend_dir)
        except Exception as e:
            print(f"💥 启动前端失败: {e}")
    except Exception as e:
        print(f"💥 启动前端失败: {e}")


def start_fullstack():
    """同时启动前后端"""
    print("\n🔥 启动全栈服务...")
    print("注意: 这将在两个终端窗口中启动服务")
    
    # 在新终端中启动后端
    backend_cmd = f"cd '{os.getcwd()}' && python backend/start_backend.py"
    
    # 在新终端中启动前端
    frontend_cmd = f"cd '{os.getcwd()}/frontend' && pnpm dev"
    
    try:
        # macOS
        if sys.platform == "darwin":
            subprocess.Popen([
                "osascript", "-e", 
                f'tell app "Terminal" to do script "{backend_cmd}"'
            ])
            subprocess.Popen([
                "osascript", "-e", 
                f'tell app "Terminal" to do script "{frontend_cmd}"'
            ])
        # Linux
        elif sys.platform.startswith("linux"):
            subprocess.Popen(["gnome-terminal", "--", "bash", "-c", backend_cmd])
            subprocess.Popen(["gnome-terminal", "--", "bash", "-c", frontend_cmd])
        # Windows
        elif sys.platform == "win32":
            subprocess.Popen(["cmd", "/c", "start", "cmd", "/k", backend_cmd])
            subprocess.Popen(["cmd", "/c", "start", "cmd", "/k", frontend_cmd])
        else:
            print("❌ 不支持的操作系统，请手动启动服务")
            return
            
        print("✅ 服务已在新终端中启动")
        print("🌐 前端: http://localhost:3000")
        print("🔧 后端: http://localhost:9000")
        print("📚 API文档: http://localhost:9000/docs")
        
    except Exception as e:
        print(f"💥 启动全栈服务失败: {e}")
        print("\n💡 手动启动命令:")
        print("   后端: python backend/start_backend.py")
        print("   前端: cd frontend && pnpm dev")


def run_tests():
    """运行测试"""
    print("\n🧪 运行测试...")
    
    test_files = [
        "backend/tests/test_basic_features.py",
        "backend/tests/test_enhanced_features.py",
        "backend/tests/test_fixed_features.py"
    ]
    
    for test_file in test_files:
        test_path = Path(test_file)
        if test_path.exists():
            print(f"\n🔍 运行测试: {test_file}")
            try:
                subprocess.run([sys.executable, str(test_path)])
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        else:
            print(f"⚠️  测试文件不存在: {test_file}")


if __name__ == "__main__":
    main()
