"""
对话管理 API 路由
Conversation Management API Routes
"""

from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from loguru import logger

from ..models.conversation import (
    ConversationThreadCreate,
    ConversationThreadFilter,
    ConversationThreadList,
    ConversationThreadResponse,
    ConversationThreadUpdate,
)
from ..models.message import ConversationHistory, MessageCreate
from ..services.conversation_service import ConversationService
from ..services.task_service import TaskService

# 创建路由器
router = APIRouter(prefix="/conversations", tags=["Conversations"])

# 依赖注入
async def get_conversation_service() -> ConversationService:
    """获取对话服务"""
    return ConversationService()

async def get_task_service() -> TaskService:
    """获取任务服务"""
    return TaskService()


@router.post("/", response_model=ConversationThreadResponse, status_code=status.HTTP_201_CREATED)
async def create_conversation(
    conversation_data: ConversationThreadCreate,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    创建新对话线程
    
    - **title**: 对话标题
    - **description**: 对话描述（可选）
    - **character_id**: 角色ID
    - **user_id**: 用户ID（可选）
    - **context**: 上下文信息
    - **settings**: 设置信息
    """
    try:
        logger.info(f"Creating new conversation: {conversation_data.title}")

        conversation = await conversation_service.create_conversation(conversation_data)

        logger.success(f"Created conversation: {conversation.id}")
        return conversation

    except Exception as e:
        logger.error(f"Failed to create conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create conversation: {str(e)}"
        )


@router.get("/", response_model=ConversationThreadList)
async def list_conversations(
    status_filter: str = None,
    user_id: str = None,
    character_id: str = None,
    is_pinned: bool = None,
    search: str = None,
    page: int = 1,
    page_size: int = 20,
    sort_by: str = "last_activity_at",
    sort_order: str = "desc",
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    获取对话列表
    
    - **status**: 状态过滤 (active, paused, completed, archived)
    - **user_id**: 用户ID过滤
    - **character_id**: 角色ID过滤
    - **is_pinned**: 是否置顶过滤
    - **search**: 搜索关键词
    - **page**: 页码
    - **page_size**: 每页大小
    - **sort_by**: 排序字段
    - **sort_order**: 排序方向 (asc, desc)
    """
    try:
        filter_params = ConversationThreadFilter(
            status=status_filter,
            user_id=user_id,
            character_id=character_id,
            is_pinned=is_pinned,
            search=search,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        conversations = await conversation_service.list_conversations(filter_params)

        logger.info(f"Listed {len(conversations.threads)} conversations")
        return conversations

    except Exception as e:
        logger.error(f"Failed to list conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list conversations: {str(e)}"
        )


@router.get("/{conversation_id}", response_model=ConversationThreadResponse)
async def get_conversation(
    conversation_id: str,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    获取对话详情
    
    - **conversation_id**: 对话ID
    """
    try:
        conversation = await conversation_service.get_conversation(conversation_id)

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found"
            )

        return conversation

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get conversation: {str(e)}"
        )


@router.put("/{conversation_id}", response_model=ConversationThreadResponse)
async def update_conversation(
    conversation_id: str,
    update_data: ConversationThreadUpdate,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    更新对话
    
    - **conversation_id**: 对话ID
    - **title**: 对话标题（可选）
    - **description**: 对话描述（可选）
    - **status**: 对话状态（可选）
    - **character_id**: 角色ID（可选）
    - **context**: 上下文信息（可选）
    - **settings**: 设置信息（可选）
    - **is_pinned**: 是否置顶（可选）
    """
    try:
        conversation = await conversation_service.update_conversation(conversation_id, update_data)

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found"
            )

        logger.info(f"Updated conversation: {conversation_id}")
        return conversation

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update conversation: {str(e)}"
        )


@router.delete("/{conversation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_conversation(
    conversation_id: str,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    删除对话（软删除）
    
    - **conversation_id**: 对话ID
    """
    try:
        success = await conversation_service.delete_conversation(conversation_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found"
            )

        logger.info(f"Deleted conversation: {conversation_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete conversation: {str(e)}"
        )


@router.get("/{conversation_id}/history", response_model=ConversationHistory)
async def get_conversation_history(
    conversation_id: str,
    limit: int = 100,
    conversation_service: ConversationService = Depends(get_conversation_service)
):
    """
    获取对话历史
    
    - **conversation_id**: 对话ID
    - **limit**: 消息数量限制
    """
    try:
        history = await conversation_service.get_conversation_history(conversation_id, limit)

        if not history:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found"
            )

        return history

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get conversation history {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get conversation history: {str(e)}"
        )


@router.post("/{conversation_id}/messages", response_model=dict[str, Any])
async def send_message(
    conversation_id: str,
    message_data: MessageCreate,
    conversation_service: ConversationService = Depends(get_conversation_service),
    task_service: TaskService = Depends(get_task_service)
):
    """
    发送消息到对话
    
    - **conversation_id**: 对话ID
    - **content**: 消息内容
    - **message_type**: 消息类型
    - **metadata**: 元数据
    - **attachments**: 附件
    """
    try:
        # 验证对话是否存在
        conversation = await conversation_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found"
            )

        # 更新对话活动时间
        await conversation_service.update_activity(conversation_id)

        # 提交对话处理任务
        task = await task_service.submit_conversation_task(
            conversation_id=conversation_id,
            message_id=message_data.conversation_id,  # 这里应该是 message_id
            content=message_data.content,
            message_type=message_data.message_type.value,
            context=message_data.metadata,
            user_id=None,  # 从认证中获取
        )

        logger.info(f"Submitted message processing task: {task.task_id}")

        return {
            "message": "Message submitted for processing",
            "task_id": task.task_id,
            "conversation_id": conversation_id,
            "status": "processing"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to send message to conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send message: {str(e)}"
        )


@router.post("/{conversation_id}/animation", response_model=dict[str, Any])
async def start_animation_generation(
    conversation_id: str,
    request_data: dict[str, Any],
    conversation_service: ConversationService = Depends(get_conversation_service),
    task_service: TaskService = Depends(get_task_service)
):
    """
    开始动画生成
    
    - **conversation_id**: 对话ID
    - **text**: 动画描述文本
    - **character_id**: 角色ID
    - **context**: 上下文信息
    """
    try:
        # 验证对话是否存在
        conversation = await conversation_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Conversation {conversation_id} not found"
            )

        # 提取请求参数
        text = request_data.get("text")
        if not text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Text is required for animation generation"
            )

        character_id = request_data.get("character_id", "default")
        context = request_data.get("context", {})
        message_id = request_data.get("message_id")

        # 更新对话活动时间
        await conversation_service.update_activity(conversation_id)

        # 提交动画生成任务
        task = await task_service.submit_animation_task(
            conversation_id=conversation_id,
            message_id=message_id,
            text=text,
            character_id=character_id,
            context=context,
            user_id=None,  # 从认证中获取
        )

        logger.info(f"Submitted animation generation task: {task.task_id}")

        return {
            "message": "Animation generation started",
            "task_id": task.task_id,
            "conversation_id": conversation_id,
            "status": "processing",
            "estimated_duration": task.estimated_duration
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start animation generation for conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start animation generation: {str(e)}"
        )
