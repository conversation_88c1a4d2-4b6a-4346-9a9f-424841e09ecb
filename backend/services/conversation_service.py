"""
对话管理服务
Conversation Management Service
"""

import uuid
from datetime import datetime

from loguru import logger
from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from ..models.conversation import (
    ConversationStatus,
    ConversationThread,
    ConversationThreadCreate,
    ConversationThreadFilter,
    ConversationThreadList,
    ConversationThreadResponse,
    ConversationThreadUpdate,
)
from ..models.message import ConversationHistory, Message, MessageResponse


class ConversationService:
    """对话管理服务"""

    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def create_conversation(
        self,
        conversation_data: ConversationThreadCreate
    ) -> ConversationThreadResponse:
        """创建新对话"""
        try:
            # 创建对话线程
            conversation = ConversationThread(
                id=uuid.uuid4(),
                title=conversation_data.title,
                description=conversation_data.description,
                character_id=conversation_data.character_id,
                user_id=conversation_data.user_id,
                session_id=conversation_data.session_id,
                context=conversation_data.context,
                settings=conversation_data.settings,
                status=ConversationStatus.ACTIVE.value,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                last_activity_at=datetime.utcnow(),
            )

            self.db.add(conversation)
            await self.db.commit()
            await self.db.refresh(conversation)

            logger.info(f"Created new conversation: {conversation.id}")

            return ConversationThreadResponse.from_orm(conversation)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create conversation: {e}")
            raise

    async def get_conversation(self, conversation_id: str) -> ConversationThreadResponse | None:
        """获取对话详情"""
        try:
            stmt = select(ConversationThread).where(
                and_(
                    ConversationThread.id == conversation_id,
                    ConversationThread.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            conversation = result.scalar_one_or_none()

            if not conversation:
                return None

            return ConversationThreadResponse.from_orm(conversation)

        except Exception as e:
            logger.error(f"Failed to get conversation {conversation_id}: {e}")
            raise

    async def update_conversation(
        self,
        conversation_id: str,
        update_data: ConversationThreadUpdate
    ) -> ConversationThreadResponse | None:
        """更新对话"""
        try:
            stmt = select(ConversationThread).where(
                and_(
                    ConversationThread.id == conversation_id,
                    ConversationThread.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            conversation = result.scalar_one_or_none()

            if not conversation:
                return None

            # 更新字段
            update_dict = update_data.dict(exclude_unset=True)
            for field, value in update_dict.items():
                setattr(conversation, field, value)

            conversation.updated_at = datetime.utcnow()

            await self.db.commit()
            await self.db.refresh(conversation)

            logger.info(f"Updated conversation: {conversation_id}")

            return ConversationThreadResponse.from_orm(conversation)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update conversation {conversation_id}: {e}")
            raise

    async def delete_conversation(self, conversation_id: str) -> bool:
        """删除对话（软删除）"""
        try:
            stmt = select(ConversationThread).where(
                and_(
                    ConversationThread.id == conversation_id,
                    ConversationThread.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            conversation = result.scalar_one_or_none()

            if not conversation:
                return False

            conversation.is_deleted = True
            conversation.updated_at = datetime.utcnow()

            await self.db.commit()

            logger.info(f"Deleted conversation: {conversation_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to delete conversation {conversation_id}: {e}")
            raise

    async def list_conversations(
        self,
        filter_params: ConversationThreadFilter
    ) -> ConversationThreadList:
        """获取对话列表"""
        try:
            # 构建查询条件
            conditions = [ConversationThread.is_deleted == False]

            if filter_params.status:
                conditions.append(ConversationThread.status == filter_params.status.value)

            if filter_params.user_id:
                conditions.append(ConversationThread.user_id == filter_params.user_id)

            if filter_params.character_id:
                conditions.append(ConversationThread.character_id == filter_params.character_id)

            if filter_params.is_pinned is not None:
                conditions.append(ConversationThread.is_pinned == filter_params.is_pinned)

            if filter_params.search:
                search_term = f"%{filter_params.search}%"
                conditions.append(
                    or_(
                        ConversationThread.title.ilike(search_term),
                        ConversationThread.description.ilike(search_term)
                    )
                )

            # 计算总数
            count_stmt = select(func.count(ConversationThread.id)).where(and_(*conditions))
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()

            # 构建排序
            sort_column = getattr(ConversationThread, filter_params.sort_by, ConversationThread.last_activity_at)
            if filter_params.sort_order == "desc":
                sort_column = desc(sort_column)

            # 分页查询
            offset = (filter_params.page - 1) * filter_params.page_size
            stmt = (
                select(ConversationThread)
                .where(and_(*conditions))
                .order_by(sort_column)
                .offset(offset)
                .limit(filter_params.page_size)
            )

            result = await self.db.execute(stmt)
            conversations = result.scalars().all()

            # 转换为响应模型
            conversation_responses = [
                ConversationThreadResponse.from_orm(conv) for conv in conversations
            ]

            # 计算分页信息
            has_next = offset + len(conversations) < total
            has_prev = filter_params.page > 1

            return ConversationThreadList(
                threads=conversation_responses,
                total=total,
                page=filter_params.page,
                page_size=filter_params.page_size,
                has_next=has_next,
                has_prev=has_prev,
            )

        except Exception as e:
            logger.error(f"Failed to list conversations: {e}")
            raise

    async def update_activity(self, conversation_id: str) -> bool:
        """更新对话活动时间"""
        try:
            stmt = select(ConversationThread).where(
                and_(
                    ConversationThread.id == conversation_id,
                    ConversationThread.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            conversation = result.scalar_one_or_none()

            if not conversation:
                return False

            conversation.last_activity_at = datetime.utcnow()
            conversation.updated_at = datetime.utcnow()

            await self.db.commit()

            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update activity for conversation {conversation_id}: {e}")
            raise

    async def get_conversation_history(
        self,
        conversation_id: str,
        limit: int = 100
    ) -> ConversationHistory | None:
        """获取对话历史"""
        try:
            # 获取对话信息
            conversation = await self.get_conversation(conversation_id)
            if not conversation:
                return None

            # 获取消息列表
            stmt = (
                select(Message)
                .where(
                    and_(
                        Message.conversation_id == conversation_id,
                        Message.is_deleted == False
                    )
                )
                .order_by(Message.created_at)
                .limit(limit)
            )

            result = await self.db.execute(stmt)
            messages = result.scalars().all()

            # 转换为响应模型
            message_responses = [MessageResponse.from_orm(msg) for msg in messages]

            # 计算统计信息
            total_tokens = sum(msg.token_count for msg in message_responses)

            return ConversationHistory(
                conversation_id=conversation_id,
                messages=message_responses,
                total_messages=len(message_responses),
                total_tokens=total_tokens,
                created_at=conversation.created_at,
                last_activity_at=conversation.last_activity_at,
            )

        except Exception as e:
            logger.error(f"Failed to get conversation history {conversation_id}: {e}")
            raise

    async def update_stats(
        self,
        conversation_id: str,
        message_count_delta: int = 0,
        token_count_delta: int = 0
    ) -> bool:
        """更新对话统计信息"""
        try:
            stmt = select(ConversationThread).where(
                and_(
                    ConversationThread.id == conversation_id,
                    ConversationThread.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            conversation = result.scalar_one_or_none()

            if not conversation:
                return False

            # 更新统计
            current_message_count = int(conversation.message_count or "0")
            current_token_count = int(conversation.total_tokens or "0")

            conversation.message_count = str(current_message_count + message_count_delta)
            conversation.total_tokens = str(current_token_count + token_count_delta)
            conversation.updated_at = datetime.utcnow()
            conversation.last_activity_at = datetime.utcnow()

            await self.db.commit()

            logger.debug(f"Updated stats for conversation {conversation_id}: +{message_count_delta} messages, +{token_count_delta} tokens")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update stats for conversation {conversation_id}: {e}")
            raise
