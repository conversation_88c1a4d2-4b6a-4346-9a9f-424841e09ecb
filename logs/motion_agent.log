2025-06-10 18:02:08 | INFO     | backend.config:validate_config:305 - Created directory: ./output/animations
2025-06-10 18:02:08 | INFO     | backend.config:validate_config:305 - Created directory: ./temp/animation_data
2025-06-10 18:02:08 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 18:02:08 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 18:02:08 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 18:02:08 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 18:02:08 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 18:02:08 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 18:02:08 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 18:02:08 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 18:02:08 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 18:02:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:02:18 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 18:02:18 | INFO     | backend.services.task_service:create_task:74 - Created task: da8c0aee-57bc-444c-9baf-c27ba90a5ab5 (animation_generation)
2025-06-10 18:02:18 | INFO     | backend.services.task_service:create_animation_task:228 - Created animation task: da8c0aee-57bc-444c-9baf-c27ba90a5ab5
2025-06-10 18:02:18 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: da8c0aee-57bc-444c-9baf-c27ba90a5ab5
2025-06-10 18:02:18 | INFO     | backend.services.task_service:update_task_status:267 - Updated task da8c0aee-57bc-444c-9baf-c27ba90a5ab5 status to running
2025-06-10 18:02:19 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 18:02:19 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:__init__:83 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 18:02:19 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 18:02:19 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:__init__:83 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:92 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:96 - Step 1: Natural Language Understanding
2025-06-10 18:02:19 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 18:02:19 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:103 - Step 2: Animator Function Processing
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:178 - Applying unified animator functions with enhanced features
2025-06-10 18:02:19 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:189 - Optimizing action transitions
2025-06-10 18:02:19 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:145 - Optimizing sequence of 1 actions...
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:109 - Step 3: Generate Blender Animation Data
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:process_animation_request:113 - Step 4: Execute Blender Animation Generation
2025-06-10 18:02:19 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:372 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749549739.json --output output/animations/animation_default_1749549739.fbx --format fbx
2025-06-10 18:02:20 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:384 - Blender animation generated: output/animations/animation_default_1749549739.fbx
2025-06-10 18:02:20 | WARNING  | backend.animation.professional_pipeline:process_animation_request:118 - Blender generation failed, creating test FBX file
2025-06-10 18:02:20 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:569 - Created test FBX file: output/animations/animation_1749549740.fbx
2025-06-10 18:02:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:124 - Step 5: FBX File Validation
2025-06-10 18:02:20 | INFO     | backend.animation.fbx_validator:validate_fbx_file:27 - Starting FBX validation for: output/animations/animation_1749549740.fbx
2025-06-10 18:02:20 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:86 - FBX validation completed for: output/animations/animation_1749549740.fbx
2025-06-10 18:02:20 | INFO     | backend.animation.professional_pipeline:process_animation_request:133 - Step 6: Animation Quality Check
2025-06-10 18:02:20 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 18:02:20 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 18:02:20 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:158 - Animation generated successfully in 0.89s
2025-06-10 18:02:20 | INFO     | backend.services.task_service:update_task_status:267 - Updated task da8c0aee-57bc-444c-9baf-c27ba90a5ab5 status to completed
2025-06-10 18:02:20 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task da8c0aee-57bc-444c-9baf-c27ba90a5ab5 status to completed
2025-06-10 18:02:20 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 18:02:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:02:25 | INFO     | backend.routers.animation:download_animation_file:249 - Download request for file: animation_1749549740.fbx
2025-06-10 18:02:25 | INFO     | backend.routers.animation:download_animation_file:264 - Serving file: animation_1749549740.fbx (1212 bytes)
2025-06-10 18:02:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:03:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:03:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:04:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:05:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:06:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:07:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:07:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:08:02 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 18:08:02 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 18:08:02 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 18:08:02 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 18:08:02 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 18:08:02 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 21:39:12 | SUCCESS  | backend.config:validate_config:325 - Configuration validation passed
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:110 - Starting Motion Agent API server with Taskiq...
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:113 - Initializing MongoDB database...
2025-06-10 21:39:12 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-10 21:39:12 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:120 - Creating database indexes...
2025-06-10 21:39:12 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-10 21:39:12 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:124 - Initializing taskiq...
2025-06-10 21:39:12 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 21:39:12 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 21:39:12 | SUCCESS  | backend.app:startup_event:134 - Motion Agent API with Taskiq initialized successfully
2025-06-10 21:39:12 | INFO     | backend.app:startup_event:135 - Motion Agent API is ready to serve requests
2025-06-10 21:39:14 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:39:39 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:39 | INFO     | backend.services.task_service:create_task:69 - Created task: 9fd71306-d94a-4d67-a90d-177ca0bcfc8a (animation_generation)
2025-06-10 21:39:39 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 9fd71306-d94a-4d67-a90d-177ca0bcfc8a
2025-06-10 21:39:39 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 9fd71306-d94a-4d67-a90d-177ca0bcfc8a
2025-06-10 21:39:39 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 9fd71306-d94a-4d67-a90d-177ca0bcfc8a status to running
2025-06-10 21:39:41 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:39:41 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:39:41 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:39:41 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 21:39:41 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:41 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 21:39:41 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 21:39:41 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749562781.json --output output/animations/animation_default_1749562781.fbx --format fbx
2025-06-10 21:39:41 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749562781.fbx
2025-06-10 21:39:41 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749562781.fbx
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 21:39:41 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749562781.fbx
2025-06-10 21:39:41 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749562781.fbx
2025-06-10 21:39:41 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 21:39:41 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 21:39:41 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 21:39:41 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.85s
2025-06-10 21:39:41 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 9fd71306-d94a-4d67-a90d-177ca0bcfc8a status to completed
2025-06-10 21:39:41 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 9fd71306-d94a-4d67-a90d-177ca0bcfc8a status to completed
2025-06-10 21:39:41 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 21:39:43 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:39:44 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:39:51 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:51 | INFO     | backend.services.task_service:create_task:69 - Created task: d2522261-549b-4358-8d3b-4912599a64a0 (animation_generation)
2025-06-10 21:39:51 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: d2522261-549b-4358-8d3b-4912599a64a0
2025-06-10 21:39:51 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: d2522261-549b-4358-8d3b-4912599a64a0
2025-06-10 21:39:51 | INFO     | backend.services.task_service:update_task_status:262 - Updated task d2522261-549b-4358-8d3b-4912599a64a0 status to running
2025-06-10 21:39:51 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:39:51 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:39:51 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:39:51 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:39:51 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 21:39:51 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:39:51 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 21:39:51 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 21:39:51 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749562791.json --output output/animations/animation_default_1749562791.fbx --format fbx
2025-06-10 21:39:51 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749562791.fbx
2025-06-10 21:39:51 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749562791.fbx
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 21:39:51 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749562791.fbx
2025-06-10 21:39:51 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749562791.fbx
2025-06-10 21:39:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 21:39:51 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 21:39:51 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 21:39:51 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.43s
2025-06-10 21:39:51 | INFO     | backend.services.task_service:update_task_status:262 - Updated task d2522261-549b-4358-8d3b-4912599a64a0 status to completed
2025-06-10 21:39:51 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task d2522261-549b-4358-8d3b-4912599a64a0 status to completed
2025-06-10 21:39:51 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-10 21:39:57 | INFO     | backend.routers.animation:download_animation_file:249 - Download request for file: animation_1749562791.fbx
2025-06-10 21:39:57 | INFO     | backend.routers.animation:download_animation_file:264 - Serving file: animation_1749562791.fbx (1212 bytes)
2025-06-10 21:40:14 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:40:45 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:41:15 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:41:45 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:42:15 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:42:44 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 21:42:46 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:42:46 | INFO     | backend.services.task_service:create_task:69 - Created task: d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27 (animation_generation)
2025-06-10 21:42:46 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27
2025-06-10 21:42:46 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27
2025-06-10 21:42:46 | INFO     | backend.services.task_service:update_task_status:262 - Updated task d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27 status to running
2025-06-10 21:42:46 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-10 21:42:46 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-10 21:42:46 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-10 21:42:46 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-10 21:42:46 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:__init__:84 - Professional Animation Pipeline initialized with enhanced features
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:93 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:97 - Step 1: Natural Language Understanding
2025-06-10 21:42:46 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: 生成一个 关羽登场 的场景，要求如下：
1. 全景，推镜，黄沙漫天，镜头缓缓推进女主，女主在画面中艰难前行，四周响起从快到慢的战鼓声，顺着声音镜头上摇，遮天蔽日的黄沙逐渐散去，时长 8s,声音 风沙声，战鼓声
2. 全景，缓推镜，风沙逐渐散开后出现伟岸的关羽骑士的轮廓，战鼓声逐渐加强。也出现士兵的呐喊声。时长2时。声音战鼓声，呐喊声
2025-06-10 21:42:46 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:104 - Step 2: Animator Function Processing
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:179 - Applying unified animator functions with enhanced features
2025-06-10 21:42:46 | INFO     | backend.animation.animator_functions:create_idle_animation:164 - Creating idle animation: stand
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:190 - Optimizing action transitions
2025-06-10 21:42:46 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:110 - Step 3: Generate Blender Animation Data
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:process_animation_request:114 - Step 4: Execute Blender Animation Generation
2025-06-10 21:42:46 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:373 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749562966.json --output output/animations/animation_default_1749562966.fbx --format fbx
2025-06-10 21:42:47 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:385 - Blender animation generated: output/animations/animation_default_1749562966.fbx
2025-06-10 21:42:47 | WARNING  | backend.animation.professional_pipeline:process_animation_request:119 - Blender generation failed, creating test FBX file
2025-06-10 21:42:47 | INFO     | backend.animation.professional_pipeline:_create_test_fbx_file:570 - Created test FBX file: output/animations/animation_1749562967.fbx
2025-06-10 21:42:47 | INFO     | backend.animation.professional_pipeline:process_animation_request:125 - Step 5: FBX File Validation
2025-06-10 21:42:47 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_1749562967.fbx
2025-06-10 21:42:47 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_1749562967.fbx
2025-06-10 21:42:47 | INFO     | backend.animation.professional_pipeline:process_animation_request:134 - Step 6: Animation Quality Check
2025-06-10 21:42:47 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-10 21:42:47 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-10 21:42:47 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:159 - Animation generated successfully in 0.87s
2025-06-10 21:42:47 | INFO     | backend.services.task_service:update_task_status:262 - Updated task d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27 status to completed
2025-06-10 21:42:47 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task d86f4cfc-1b0d-4c3c-88b3-23ec27c4ca27 status to completed
2025-06-10 21:42:47 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
