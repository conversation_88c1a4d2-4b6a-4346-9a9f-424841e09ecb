'use client';

import { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import Navigation from '@/components/Navigation';
import MotionGenerator from '@/components/MotionGenerator';
import MotionGeneratorAdvanced from '@/components/MotionGeneratorAdvanced';
import AnimationPreview from '@/components/AnimationPreview';
import ExampleRequests from '@/components/ExampleRequests';
import ConversationManager from '@/components/ConversationManager';
import TaskMonitor from '@/components/TaskMonitor';
import StatusDashboard from '@/components/StatusDashboard';
import { AnimationResponse, HealthStatus, ConversationThread } from '@/types/motion';
import { checkHealth } from '@/lib/api';
import { Activity, AlertCircle, CheckCircle } from 'lucide-react';

export default function Home() {
  const [currentPage, setCurrentPage] = useState('home');
  const [response, setResponse] = useState<AnimationResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedExample, setSelectedExample] = useState<string>('');
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [healthData, setHealthData] = useState<HealthStatus | null>(null);
  const [selectedConversation, setSelectedConversation] = useState<ConversationThread | null>(null);

  // Check backend health on component mount
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const health = await checkHealth();
        setHealthData(health);
        setBackendStatus('online');
      } catch (error) {
        console.error('Backend health check failed:', error);
        setBackendStatus('offline');
      }
    };

    checkBackendHealth();
    // Check health every 30 seconds
    const interval = setInterval(checkBackendHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleExampleSelect = (text: string) => {
    setSelectedExample(text);
    // Clear previous results when selecting new example
    setResponse(null);
    setError(null);
  };

  const handlePageChange = (page: string) => {
    setCurrentPage(page);
  };

  const handleConversationSelect = (conversation: ConversationThread) => {
    setSelectedConversation(conversation);
  };



  const renderPageContent = () => {
    switch (currentPage) {
      case 'home':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Input and Examples */}
            <div className="space-y-8">
              <MotionGenerator
                onResponse={setResponse}
                onLoading={setLoading}
                onError={setError}
                selectedExample={selectedExample}
              />
              <ExampleRequests onSelectExample={handleExampleSelect} />
            </div>

            {/* Right Column - Preview */}
            <div>
              <AnimationPreview
                response={response}
                loading={loading}
                error={error}
              />
            </div>
          </div>
        );

      case 'motion':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <MotionGeneratorAdvanced />
            </div>
            <div>
              <AnimationPreview
                response={response}
                loading={loading}
                error={error}
              />
            </div>
          </div>
        );

      case 'conversations':
        return (
          <div className="h-[calc(100vh-200px)]">
            <ConversationManager onSelectConversation={handleConversationSelect} />
          </div>
        );

      case 'tasks':
        return (
          <div className="h-[calc(100vh-200px)]">
            <TaskMonitor conversationId={selectedConversation?.id} />
          </div>
        );

      case 'status':
        return <StatusDashboard />;

      default:
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h2>
            <p className="text-gray-600">The requested page could not be found.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-right" />

      {/* Navigation */}
      <Navigation
        currentPage={currentPage}
        onPageChange={handlePageChange}
        backendStatus={backendStatus}
        healthData={healthData}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderPageContent()}

        {/* Backend Status Details - Only show on home page */}
        {currentPage === 'home' && healthData && backendStatus === 'online' && (
          <div className="mt-8 bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Backend Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-sm text-gray-600">NLU Pipeline</div>
                <div className="font-semibold text-green-700">
                  {healthData.nlu_pipeline || 'Ready'}
                </div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-sm text-gray-600">LangGraph</div>
                <div className="font-semibold text-blue-700">
                  {healthData.langgraph_pipeline || 'Ready'}
                </div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-sm text-gray-600">Professional Animator</div>
                <div className="font-semibold text-purple-700">
                  {healthData.professional_animator || 'Ready'}
                </div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-sm text-gray-600">Version</div>
                <div className="font-semibold text-orange-700">{healthData.version}</div>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>Motion Agent - Professional 3D Animation Generator</p>
            <p className="text-sm mt-2">
              Powered by MotionGPT + Motion Agent + MoDi + Blender + Python
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
